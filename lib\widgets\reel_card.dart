import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import '../models/reel.dart';

class ReelCard extends StatefulWidget {
  final Reel reel;

  const ReelCard({super.key, required this.reel});

  @override
  State<ReelCard> createState() => _ReelCardState();
}

class _ReelCardState extends State<ReelCard> {
  late VideoPlayerController _controller;
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.networkUrl(Uri.parse(widget.reel.videoUrl))
      ..initialize().then((_) {
        setState(() {});
        _controller.setLooping(true);
        _controller.play();
        _isPlaying = true;
      });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _togglePlayPause() {
    setState(() {
      if (_controller.value.isPlaying) {
        _controller.pause();
        _isPlaying = false;
      } else {
        _controller.play();
        _isPlaying = true;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _togglePlayPause,
      child: Stack(
        fit: StackFit.expand,
        children: [
          _controller.value.isInitialized
              ? AspectRatio(
                  aspectRatio: _controller.value.aspectRatio,
                  child: VideoPlayer(_controller),
                )
              : Image.network(
                  widget.reel.coverUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.black,
                      child: const Center(
                        child: Text(
                          '无法加载视频或封面',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    );
                  },
                ),
          if (!_isPlaying)
            const Center(
              child: Icon(
                Icons.play_arrow,
                color: Colors.white,
                size: 80.0,
              ),
            ),
          // 视频信息和用户操作
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.reel.title, // 使用短剧标题作为用户名
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  widget.reel.introduce,
                  style: const TextStyle(color: Colors.white, fontSize: 16),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.music_note, color: Colors.white, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      widget.reel.tags.isNotEmpty ? widget.reel.tags.join(', ') : '无标签', // 使用标签作为音乐标题
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // 右侧操作按钮
          Align(
            alignment: Alignment.centerRight,
            child: Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Column(
                    children: [
                      CircleAvatar(
                        backgroundImage: NetworkImage(widget.reel.coverUrl), // 使用封面作为头像
                        radius: 24,
                      ),
                      const SizedBox(height: 8),
                      const Icon(Icons.add_circle, color: Colors.white, size: 24),
                    ],
                  ),
                  const SizedBox(height: 24),
                  Column(
                    children: [
                      const Icon(Icons.favorite, color: Colors.white, size: 36),
                      Text('${widget.reel.uploadNum}', style: const TextStyle(color: Colors.white)), // 使用upload_num作为点赞数
                    ],
                  ),
                  const SizedBox(height: 16),
                  Column(
                    children: [
                      const Icon(Icons.comment, color: Colors.white, size: 36),
                      Text('${widget.reel.playletId}', style: const TextStyle(color: Colors.white)), // 使用playlet_id作为评论数
                    ],
                  ),
                  const SizedBox(height: 16),
                  const Icon(Icons.share, color: Colors.white, size: 36),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}