import 'package:flutter/material.dart';
import 'package:reels_app/models/reel.dart';
import 'package:reels_app/services/reel_service.dart';
import 'package:reels_app/screens/play_screen.dart';

class ForYouScreen extends StatefulWidget {
  const ForYouScreen({super.key});

  @override
  State<ForYouScreen> createState() => _ForYouScreenState();
}

class _ForYouScreenState extends State<ForYouScreen> {
  final PageController _pageController = PageController();
  final List<Reel> _reels = [];
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _loadReels();
  }

  void _loadReels() {
    _reels.addAll(ReelService.getMockReels());
    // 不再默认初始化播放器，只加载数据
    setState(() {});
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_reels.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          PageView.builder(
            controller: _pageController,
            scrollDirection: Axis.vertical,
            itemCount: _reels.length,
            onPageChanged: _onPageChanged,
            itemBuilder: (context, index) {
              final reel = _reels[index];
              return _buildVideoPlayer(reel);
            },
          ),
          _buildTopBar(),
          _buildRightSideBar(),
          _buildBottomBar(),
        ],
      ),
    );
  }

  Widget _buildVideoPlayer(Reel reel) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PlayScreen(
              videoUrl: reel.videoUrl,
              coverUrl: reel.coverUrl,
              title: reel.title,
              description: reel.introduce,
              likes: reel.uploadNum,
              comments: reel.playletId % 100,
              shares: reel.playletId % 50,
              authorName: 'Author ${reel.playletId % 10}',
              authorAvatar: reel.coverUrl,
              episode: reel.uploadNum,
            ),
          ),
        );
      },
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: NetworkImage(reel.coverUrl),
            fit: BoxFit.cover,
          ),
        ),
        child: const Center(
          child: Icon(
            Icons.play_circle_outline,
            color: Colors.white,
            size: 80,
          ),
        ),
      ),
    );
  }

  Widget _buildTopBar() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 10,
      left: 0,
      right: 0,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'For You',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.search, color: Colors.white),
                  onPressed: () {},
                ),
                IconButton(
                  icon: const Icon(Icons.more_vert, color: Colors.white),
                  onPressed: () {},
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRightSideBar() {
    final reel = _reels[_currentPage];
    return Positioned(
      right: 16,
      bottom: 100,
      child: Column(
        children: [
          _buildActionButton(Icons.favorite_border, '${reel.uploadNum}'),
          const SizedBox(height: 20),
          _buildActionButton(Icons.comment_outlined, '${reel.playletId % 100}'),
          const SizedBox(height: 20),
          _buildActionButton(Icons.share, '${reel.playletId % 50}'),
          const SizedBox(height: 20),
          _buildActionButton(Icons.download, ''),
          const SizedBox(height: 20),
          _buildUserAvatar(reel.coverUrl),
        ],
      ),
    );
  }

  Widget _buildBottomBar() {
    final reel = _reels[_currentPage];
    return Positioned(
      left: 16,
      right: 80,
      bottom: 30,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            reel.title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          Text(
            reel.introduce,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(IconData icon, String label) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 32),
        if (label.isNotEmpty) ...[
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        ],
      ],
    );
  }

  Widget _buildUserAvatar(String imageUrl) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 2),
      ),
      child: ClipOval(
        child: Image.network(
          imageUrl,
          fit: BoxFit.cover,
        ),
      ),
    );
  }
}
