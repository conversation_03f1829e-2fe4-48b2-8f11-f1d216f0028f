import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';

class PlayScreen extends StatefulWidget {
  final String? videoUrl;
  final String? coverUrl;
  final String? title;
  final String? description;
  final int? likes;
  final int? comments;
  final int? shares;
  final String? authorName;
  final String? authorAvatar;
  final int? episode;

  const PlayScreen({
    super.key,
    this.videoUrl,
    this.coverUrl,
    this.title,
    this.description,
    this.likes,
    this.comments,
    this.shares,
    this.authorName,
    this.authorAvatar,
    this.episode,
  });

  @override
  State<PlayScreen> createState() => _PlayScreenState();
}

class _PlayScreenState extends State<PlayScreen> {
  late VideoPlayerController _videoPlayerController;
  ChewieController? _chewieController;
  bool _isPlaying = false;
  bool _showEpisodeList = false;
  int _currentEpisode = 1;
  String _selectedRange = '01-25';
  bool _isLiked = false;
  int _currentLikes = 0;

  // 默认视频URL
  final String _defaultVideoUrl = 'https://zshipricf.farsunpteltd.com/playlet-hls/hls_1747208892_1_91841.m3u8?verify=1748958770-zW%2FB1NtCZdti4OdY59VVbcf5kTekbzoS7P33o2sLM2M%3D';
  final String _defaultCoverUrl = 'https://zshipubcdn.farsunpteltd.com/playlet/1748609140_2xhXGXhGEe.jpg';
  final String _defaultTitle = 'One Night, One Deal, One Billio...';
  final String _defaultDescription = 'All 55 EP';

  @override
  void initState() {
    super.initState();
    _currentEpisode = widget.episode ?? 1;
    _currentLikes = widget.likes ?? 0;
    // 设置系统UI覆盖样式
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      systemNavigationBarColor: Colors.black,
      systemNavigationBarDividerColor: Colors.transparent,
    ));
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    try {
      final videoUrl = widget.videoUrl ?? _defaultVideoUrl;
      debugPrint('Initializing video player with URL: $videoUrl');
      _videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(videoUrl));
      await _videoPlayerController.initialize();

      debugPrint('Video player initialized successfully');
      debugPrint('Video duration: ${_videoPlayerController.value.duration}');
      debugPrint('Video aspect ratio: ${_videoPlayerController.value.aspectRatio}');

      _chewieController = ChewieController(
        videoPlayerController: _videoPlayerController,
        autoPlay: true,
        looping: true,
        showControls: false,
        allowFullScreen: false,
        aspectRatio: _videoPlayerController.value.aspectRatio,
        placeholder: Container(
          color: Colors.black,
          child: Center(
            child: Image.network(
              widget.coverUrl ?? _defaultCoverUrl,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return const Icon(
                  Icons.image_not_supported,
                  color: Colors.white54,
                  size: 64,
                );
              },
            ),
          ),
        ),
      );

      _videoPlayerController.addListener(_videoListener);

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      debugPrint('Error initializing video player: $e');
    }
  }

  void _videoListener() {
    if (_videoPlayerController.value.isPlaying != _isPlaying) {
      setState(() {
        _isPlaying = _videoPlayerController.value.isPlaying;
      });
    }
  }

  @override
  void dispose() {
    _videoPlayerController.removeListener(_videoListener);
    _videoPlayerController.dispose();
    _chewieController?.dispose();
    // 恢复系统UI
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: SystemUiOverlay.values);
    super.dispose();
  }

  void _toggleEpisodeList() {
    setState(() {
      _showEpisodeList = !_showEpisodeList;
    });
  }

  void _selectEpisode(int episode) {
    setState(() {
      _currentEpisode = episode;
      _showEpisodeList = false;
    });
    // 这里可以添加切换集数的逻辑
  }

  void _selectRange(String range) {
    setState(() {
      _selectedRange = range;
    });
  }

  void _toggleLike() {
    setState(() {
      _isLiked = !_isLiked;
      _currentLikes += _isLiked ? 1 : -1;
    });
  }

  List<int> _getEpisodesForRange(String range) {
    switch (range) {
      case '01-25':
        return List.generate(25, (index) => index + 1);
      case '26-50':
        return List.generate(25, (index) => index + 26);
      case '51-55':
        return List.generate(5, (index) => index + 51);
      default:
        return List.generate(25, (index) => index + 1);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBody: true,
      body: Container(
        color: Colors.black,
        child: GestureDetector(
          onTap: () {
            if (_showEpisodeList) {
              _toggleEpisodeList();
            }
          },
          behavior: HitTestBehavior.opaque,
          child: Stack(
            fit: StackFit.expand,
            children: [
              // Video Player
              if (_chewieController != null)
                Container(
                  color: Colors.black,
                  child: Chewie(
                    controller: _chewieController!,
                  ),
                )
              else
                const Center(child: CircularProgressIndicator()),

              // 顶部返回按钮和标题
              Positioned(
                top: MediaQuery.of(context).padding.top + 10,
                left: 16,
                right: 16,
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Icon(
                          Icons.arrow_back,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        widget.title ?? _defaultTitle,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),

              // 底部集数选择界面
              if (_showEpisodeList) _buildEpisodeList(),

              // 右侧操作栏
              if (!_showEpisodeList) _buildRightSideBar(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEpisodeList() {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.9),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // 拖拽指示器
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[600],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // 关闭按钮
            Align(
              alignment: Alignment.topRight,
              child: GestureDetector(
                onTap: _toggleEpisodeList,
                child: Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[800],
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ),

            // 剧集信息
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  // 剧集封面
                  Container(
                    width: 60,
                    height: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      image: DecorationImage(
                        image: NetworkImage(widget.coverUrl ?? _defaultCoverUrl),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // 剧集信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.title ?? _defaultTitle,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          widget.description ?? _defaultDescription,
                          style: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // 集数范围选择
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  _buildRangeButton('01-25'),
                  const SizedBox(width: 12),
                  _buildRangeButton('26-50'),
                  const SizedBox(width: 12),
                  _buildRangeButton('51-55'),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // 集数网格
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 5,
                    childAspectRatio: 1.0,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: _getEpisodesForRange(_selectedRange).length,
                  itemBuilder: (context, index) {
                    final episode = _getEpisodesForRange(_selectedRange)[index];
                    final isCurrentEpisode = episode == _currentEpisode;

                    return GestureDetector(
                      onTap: () => _selectEpisode(episode),
                      child: Container(
                        decoration: BoxDecoration(
                          color: isCurrentEpisode
                              ? const Color(0xFFFFD700) // 金色高亮
                              : Colors.grey[800],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Center(
                          child: Text(
                            '$episode',
                            style: TextStyle(
                              color: isCurrentEpisode ? Colors.black : Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),

            // 底部安全区域
            SizedBox(height: MediaQuery.of(context).padding.bottom),
          ],
        ),
      ),
    );
  }

  Widget _buildRightSideBar() {
    return Positioned(
      right: 16,
      bottom: 100,
      child: Column(
        children: [
          _buildActionButton(
            _isLiked ? Icons.favorite : Icons.favorite_border,
            '$_currentLikes',
            onTap: _toggleLike,
          ),
          const SizedBox(height: 20),
          _buildActionButton(
            Icons.comment_outlined,
            '${widget.comments ?? 0}',
          ),
          const SizedBox(height: 20),
          _buildActionButton(
            Icons.share,
            '${widget.shares ?? 0}',
          ),
          const SizedBox(height: 20),
          _buildActionButton(
            Icons.download,
            '',
          ),
          const SizedBox(height: 20),
          _buildUserAvatar(widget.authorAvatar ?? widget.coverUrl ?? _defaultCoverUrl),
          const SizedBox(height: 20),
          // 集数按钮
          GestureDetector(
            onTap: _toggleEpisodeList,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.list,
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '$_currentEpisode',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(IconData icon, String label, {VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 32),
          if (label.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildUserAvatar(String imageUrl) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 2),
      ),
      child: ClipOval(
        child: Image.network(
          imageUrl,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return const Icon(
              Icons.person,
              color: Colors.white,
              size: 24,
            );
          },
        ),
      ),
    );
  }

  Widget _buildRangeButton(String range) {
    final isSelected = _selectedRange == range;
    return GestureDetector(
      onTap: () => _selectRange(range),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFFD700) : Colors.grey[800],
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          range,
          style: TextStyle(
            color: isSelected ? Colors.black : Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
