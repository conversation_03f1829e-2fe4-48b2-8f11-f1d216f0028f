{"buildFiles": ["F:\\tools\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["F:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\project\\python\\test\\app\\reels_app\\android\\app\\.cxx\\Debug\\4p3e1b6f\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["F:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\project\\python\\test\\app\\reels_app\\android\\app\\.cxx\\Debug\\4p3e1b6f\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "F:\\android\\sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "F:\\android\\sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}