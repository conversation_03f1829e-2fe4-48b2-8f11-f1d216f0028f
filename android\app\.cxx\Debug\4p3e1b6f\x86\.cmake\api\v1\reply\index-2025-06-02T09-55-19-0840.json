{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/android/sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "F:/android/sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "F:/android/sdk/cmake/3.22.1/bin/ctest.exe", "root": "F:/android/sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-5bcfb7722d24715fac56.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-4bac6cb001449bc4ec28.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-492f933df7bf8060882c.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-4bac6cb001449bc4ec28.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-492f933df7bf8060882c.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-5bcfb7722d24715fac56.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}