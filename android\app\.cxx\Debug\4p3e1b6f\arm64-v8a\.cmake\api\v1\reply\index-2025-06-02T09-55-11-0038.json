{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/android/sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "F:/android/sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "F:/android/sdk/cmake/3.22.1/bin/ctest.exe", "root": "F:/android/sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-ee024630057220be8bf5.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-1f048e7eaa1a18753139.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-66588d39d66405b2c208.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-1f048e7eaa1a18753139.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-66588d39d66405b2c208.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-ee024630057220be8bf5.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}