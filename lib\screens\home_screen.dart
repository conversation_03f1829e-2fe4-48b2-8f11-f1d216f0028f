import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:reels_app/models/reel.dart';
import 'package:reels_app/screens/play_screen.dart';
import '../services/reel_service.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final PageController _featuredController = PageController(viewportFraction: 0.9);
  final List<String> _categories = ['Home', 'New', 'Original', 'Asian'];
  int _selectedCategoryIndex = 0;
  List<Reel> _featuredReels = [];
  List<Reel> _gridReels = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    // 加载特色短剧
    _featuredReels = ReelService.getMockReels().sublist(0, 3);
    // 加载网格短剧
    _gridReels = ReelService.getMockReels().sublist(3);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        title: _buildSearchBar(),
        actions: [
          _buildNotificationButton(),
          const SizedBox(width: 8),
          _buildVipButton(),
          const SizedBox(width: 12),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCategoryTabs(),
            const SizedBox(height: 16),
            _buildFeaturedSection(),
            const SizedBox(height: 24),
            _buildGridSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        children: [
          const Icon(Icons.search, color: Colors.grey, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: TextField(
              style: const TextStyle(color: Colors.white, fontSize: 14),
              decoration: InputDecoration(
                hintText: 'Love at 50:From Janitor to Billio...',
                hintStyle: TextStyle(color: Colors.grey[600], fontSize: 13),
                border: InputBorder.none,
                isDense: true,
                contentPadding: const EdgeInsets.only(bottom: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationButton() {
    return Stack(
      children: [
        const Icon(Icons.card_giftcard, color: Colors.white, size: 24),
        Positioned(
          right: 0,
          top: 0,
          child: Container(
            padding: const EdgeInsets.all(2),
            decoration: const BoxDecoration(
              color: Colors.red,
              shape: BoxShape.circle,
            ),
            child: const Text(
              '20',
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildVipButton() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.amber[700],
        borderRadius: BorderRadius.circular(4),
      ),
      child: const Row(
        children: [
          Icon(Icons.shield, color: Colors.white, size: 16),
          SizedBox(width: 4),
          Text(
            'VIP',
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryTabs() {
    return SizedBox(
      height: 40,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final isSelected = index == _selectedCategoryIndex;
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedCategoryIndex = index;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              margin: const EdgeInsets.only(left: 12),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: isSelected ? Colors.orange : Colors.transparent,
                    width: 2,
                  ),
                ),
              ),
              child: Text(
                _categories[index],
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.grey,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontSize: 16,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFeaturedSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'Featured',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 200,
          child: PageView.builder(
            controller: _featuredController,
            itemCount: _featuredReels.length,
            itemBuilder: (context, index) {
              final reel = _featuredReels[index];
              return _buildFeaturedItem(reel);
            },
          ),
        ),
        const SizedBox(height: 8),
        Center(
          child: SmoothPageIndicator(
            controller: _featuredController,
            count: _featuredReels.length,
            effect: const WormEffect(
              dotColor: Colors.grey,
              activeDotColor: Colors.orange,
              dotHeight: 6,
              dotWidth: 6,
              spacing: 8,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturedItem(Reel reel) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PlayScreen(
              videoUrl: 'https://zshipricf.farsunpteltd.com/playlet-hls/hls_1747208892_1_91841.m3u8?verify=1748873988-huVPOxDof7BSnivJCOjM1icFiBfPweeFbpDxRTSp6vU%3D',
              coverUrl: reel.coverUrl,
              title: reel.title,
              description: reel.introduce,
              likes: reel.uploadNum,
              comments: reel.playletId % 100,
              shares: reel.playletId % 50,
              authorName: 'Author ${reel.playletId % 10}',
              authorAvatar: reel.coverUrl,
              episode: reel.uploadNum,
            ),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          image: DecorationImage(
            image: CachedNetworkImageProvider(reel.coverUrl),
            fit: BoxFit.cover,
          ),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [
                Colors.black.withValues(alpha: 0.8),
                Colors.transparent,
              ],
            ),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                reel.title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Text(
                reel.introduce.length > 100
                    ? '${reel.introduce.substring(0, 100)}...'
                    : reel.introduce,
                style: TextStyle(
                  color: Colors.grey[300],
                  fontSize: 12,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGridSection() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 12),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3, // 改为3列
        childAspectRatio: 0.6, // 调整宽高比
        crossAxisSpacing: 6, // 调整列间距
        mainAxisSpacing: 6,  // 调整行间距
      ),
      itemCount: _gridReels.length,
      itemBuilder: (context, index) {
        return _buildGridItem(_gridReels[index]);
      },
    );
  }

  Widget _buildGridItem(Reel reel) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PlayScreen(
              videoUrl: 'https://zshipricf.farsunpteltd.com/playlet-hls/hls_1747208892_1_91841.m3u8?verify=1748873988-huVPOxDof7BSnivJCOjM1icFiBfPweeFbpDxRTSp6vU%3D',
              coverUrl: reel.coverUrl,
              title: reel.title,
              description: reel.introduce,
              likes: reel.uploadNum,
              comments: reel.playletId % 100, // 模拟评论数
              shares: reel.playletId % 50,   // 模拟分享数
              authorName: 'Author ${reel.playletId % 10}',
              authorAvatar: reel.coverUrl,
              episode: reel.uploadNum,
            ),
          ),
        );
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: CachedNetworkImage(
                imageUrl: reel.coverUrl,
                fit: BoxFit.cover,
                width: double.infinity,
                placeholder: (context, url) => Container(
                  color: Colors.grey[800],
                  child: const Center(
                    child: CircularProgressIndicator(color: Colors.orange),
                  ),
                ),
                errorWidget: (context, url, error) => const Icon(Icons.error),
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            reel.title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            '${reel.uploadNum} episodes',
            style: TextStyle(
              color: Colors.grey[500],
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}
