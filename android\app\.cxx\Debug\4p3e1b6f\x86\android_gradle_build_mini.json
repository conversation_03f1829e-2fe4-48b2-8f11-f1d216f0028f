{"buildFiles": ["F:\\tools\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["F:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\project\\python\\test\\app\\reels_app\\android\\app\\.cxx\\Debug\\4p3e1b6f\\x86", "clean"]], "buildTargetsCommandComponents": ["F:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\project\\python\\test\\app\\reels_app\\android\\app\\.cxx\\Debug\\4p3e1b6f\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}