import 'package:flutter/material.dart';
import 'package:reels_app/models/reel.dart';
import 'package:reels_app/services/reel_service.dart';
import 'package:reels_app/widgets/reel_card.dart';

class ReelsHomePage extends StatefulWidget {
  const ReelsHomePage({super.key});

  @override
  State<ReelsHomePage> createState() => _ReelsHomePageState();
}

class _ReelsHomePageState extends State<ReelsHomePage> {
  final PageController _pageController = PageController();
  late List<Reel> _reels;

  @override
  void initState() {
    super.initState();
    _reels = ReelService.getMockReels();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView.builder(
        controller: _pageController,
        scrollDirection: Axis.vertical,
        itemCount: _reels.length,
        itemBuilder: (context, index) {
          return ReelCard(reel: _reels[index]);
        },
      ),
      bottomNavigationBar: BottomAppBar(
        color: Colors.black,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: const [
            Icon(Icons.home, color: Colors.white),
            Icon(Icons.search, color: Colors.white),
            Icon(Icons.add_box, color: Colors.white),
            Icon(Icons.message, color: Colors.white),
            Icon(Icons.person, color: Colors.white),
          ],
        ),
      ),
    );
  }
}