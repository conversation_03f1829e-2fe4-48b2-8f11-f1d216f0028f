class Reel {
  final int playletId;
  final String title;
  final String coverUrl; // 视频封面URL
  final int uploadNum;
  final String introduce;
  final List<String> tags; // 标签列表
  final bool isPlayletTrailer;
  final String videoUrl; // 模拟的视频URL，因为JSON中没有提供

  Reel({
    required this.playletId,
    required this.title,
    required this.coverUrl,
    required this.uploadNum,
    required this.introduce,
    this.tags = const [],
    this.isPlayletTrailer = false,
    required this.videoUrl, // 暂时保留，需要模拟或从其他地方获取
  });

  factory Reel.fromJson(Map<String, dynamic> json) {
    List<String> tagList = [];
    if (json['tag_list'] != null) {
      tagList = (json['tag_list'] as List)
          .map((tagJson) => tagJson['name'] as String)
          .toList();
    }

    // 使用新的HLS视频URL
    final String videoUrl = json['videoUrl'] as String? ?? 'https://zshipricf.farsunpteltd.com/playlet-hls/hls_1747208892_1_91841.m3u8?verify=1748958770-zW%2FB1NtCZdti4OdY59VVbcf5kTekbzoS7P33o2sLM2M%3D';

    return Reel(
      playletId: json['playlet_id'] as int,
      title: json['title'] as String,
      coverUrl: json['cover'] as String,
      uploadNum: json['upload_num'] as int,
      introduce: json['introduce'] as String,
      tags: tagList,
      isPlayletTrailer: json['is_playlet_trailer'] as bool,
      videoUrl: videoUrl, // 使用上面定义的 videoUrl 变量
    );
  }
}